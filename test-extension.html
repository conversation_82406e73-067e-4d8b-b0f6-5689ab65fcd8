<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome Extension Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }
        .step-number {
            display: inline-block;
            width: 25px;
            height: 25px;
            background: #007bff;
            color: white;
            text-align: center;
            line-height: 25px;
            border-radius: 50%;
            margin-right: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🚀 Chrome Extension Product Crawler - Test Guide</h1>
    
    <div class="test-section success">
        <h2>✅ Phase 1 Implementation Complete!</h2>
        <p>The Chrome Extension Product Crawler foundation has been successfully implemented. Follow the steps below to test the extension.</p>
    </div>

    <div class="test-section">
        <h2>📋 Testing Checklist</h2>
        
        <div class="step">
            <span class="step-number">1</span>
            <strong>Load Extension in Chrome</strong>
            <ul>
                <li>Open Chrome and go to <code>chrome://extensions/</code></li>
                <li>Enable "Developer mode" (toggle in top right)</li>
                <li>Click "Load unpacked"</li>
                <li>Select the <code>tts-chrome-extension</code> directory</li>
                <li>Verify the extension appears in the list</li>
            </ul>
        </div>

        <div class="step">
            <span class="step-number">2</span>
            <strong>Check Extension Icon</strong>
            <ul>
                <li>Look for the extension icon in Chrome toolbar</li>
                <li>Click the icon to open the popup</li>
                <li>Verify the popup displays correctly</li>
            </ul>
        </div>

        <div class="step">
            <span class="step-number">3</span>
            <strong>Test Authentication Flow</strong>
            <ul>
                <li>Click "Login" in the extension popup</li>
                <li>Verify it opens the web application login page</li>
                <li>Complete login process</li>
                <li>Check if extension recognizes authentication</li>
            </ul>
        </div>

        <div class="step">
            <span class="step-number">4</span>
            <strong>Test Product Page Detection</strong>
            <ul>
                <li>Navigate to an Etsy product page (e.g., <code>https://www.etsy.com/listing/...</code>)</li>
                <li>Open extension popup</li>
                <li>Verify it shows "Etsy" badge and "Product page detected"</li>
                <li>Repeat for eBay and Amazon product pages</li>
            </ul>
        </div>

        <div class="step">
            <span class="step-number">5</span>
            <strong>Test Product Extraction</strong>
            <ul>
                <li>On a product page, click "Extract Product" button</li>
                <li>Monitor browser console for any errors</li>
                <li>Check if extraction completes successfully</li>
                <li>Verify data is sent to backend (if running)</li>
            </ul>
        </div>
    </div>

    <div class="test-section warning">
        <h2>⚠️ Prerequisites for Full Testing</h2>
        <ul>
            <li><strong>Backend Server</strong>: Start the NestJS backend on <code>http://localhost:3001</code></li>
            <li><strong>Frontend Server</strong>: Start the Next.js frontend on <code>http://localhost:3000</code></li>
            <li><strong>Database</strong>: Ensure PostgreSQL is running with the new entities</li>
            <li><strong>Authentication</strong>: Have a valid user account for testing</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔧 Troubleshooting</h2>
        
        <h3>Common Issues:</h3>
        
        <div class="step">
            <strong>Extension Won't Load</strong>
            <ul>
                <li>Check for syntax errors in <code>manifest.json</code></li>
                <li>Verify all referenced files exist</li>
                <li>Check Chrome DevTools for error messages</li>
            </ul>
        </div>

        <div class="step">
            <strong>Service Worker Errors</strong>
            <ul>
                <li>Go to <code>chrome://extensions/</code></li>
                <li>Click "Inspect views: service worker" under your extension</li>
                <li>Check console for error messages</li>
            </ul>
        </div>

        <div class="step">
            <strong>Content Script Not Working</strong>
            <ul>
                <li>Open DevTools on the product page</li>
                <li>Check console for content script errors</li>
                <li>Verify the page URL matches the manifest patterns</li>
            </ul>
        </div>

        <div class="step">
            <strong>API Communication Issues</strong>
            <ul>
                <li>Verify backend server is running</li>
                <li>Check Network tab in DevTools for failed requests</li>
                <li>Ensure CORS is properly configured</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 Expected Behavior</h2>
        
        <h3>When Working Correctly:</h3>
        <ul>
            <li>Extension popup opens without errors</li>
            <li>Marketplace detection works on product pages</li>
            <li>Authentication flow completes successfully</li>
            <li>Product extraction captures title, images, and metadata</li>
            <li>Data is successfully sent to backend API</li>
            <li>No console errors in browser or service worker</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>📊 Test Results</h2>
        <p>Use this section to document your test results:</p>
        
        <div class="step">
            <strong>Extension Loading:</strong> ⬜ Pass ⬜ Fail<br>
            <em>Notes: ________________________________</em>
        </div>
        
        <div class="step">
            <strong>Popup Interface:</strong> ⬜ Pass ⬜ Fail<br>
            <em>Notes: ________________________________</em>
        </div>
        
        <div class="step">
            <strong>Authentication:</strong> ⬜ Pass ⬜ Fail<br>
            <em>Notes: ________________________________</em>
        </div>
        
        <div class="step">
            <strong>Page Detection:</strong> ⬜ Pass ⬜ Fail<br>
            <em>Notes: ________________________________</em>
        </div>
        
        <div class="step">
            <strong>Product Extraction:</strong> ⬜ Pass ⬜ Fail<br>
            <em>Notes: ________________________________</em>
        </div>
    </div>

    <div class="test-section success">
        <h2>🚀 Next Steps</h2>
        <p>After successful testing of Phase 1:</p>
        <ul>
            <li><strong>Phase 2</strong>: Enhanced extraction functionality and error handling</li>
            <li><strong>Phase 3</strong>: Web application integration and React components</li>
            <li><strong>Phase 4</strong>: Automated crawling and advanced features</li>
            <li><strong>Phase 5</strong>: Testing, optimization, and Chrome Web Store deployment</li>
        </ul>
    </div>

    <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666;">
        <p>Chrome Extension Product Crawler - Phase 1 Testing Guide</p>
        <p>Generated: <script>document.write(new Date().toLocaleDateString())</script></p>
    </footer>
</body>
</html>
